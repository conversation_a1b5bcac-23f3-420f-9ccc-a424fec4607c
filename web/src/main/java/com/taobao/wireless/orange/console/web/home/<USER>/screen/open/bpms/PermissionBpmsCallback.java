package com.taobao.wireless.orange.console.web.home.module.screen.open.bpms;

import com.alibaba.citrus.service.requestcontext.parser.ParameterParser;
import com.taobao.wireless.orange.console.manager.api.model.ErrorCodeConstant;
import com.taobao.wireless.orange.console.manager.api.model.Result;
import com.taobao.wireless.orange.console.manager.service.PermissionApplyService;
import com.taobao.wireless.orange.console.manager.util.logger.CommonLoggerUtil;
import com.taobao.wireless.orange.console.web.home.module.screen.open.AbstractOpenApiBase;
import com.taobao.wireless.orange.core.exception.BusinessException;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class PermissionBpmsCallback  extends AbstractOpenApiBase {
    private static final Logger logger = LoggerFactory.getLogger(PermissionBpmsCallback.class);

    @Autowired
    private PermissionApplyService permissionApplyService;

    @Override
    protected Result call(ParameterParser requestParams) throws Exception {
        String permissionRole = requestParams.getString("permissionRole");
        String objectKey = requestParams.getString("objectKey");
        String procInstId = requestParams.getString("procInstId");
        if (StringUtils.isBlank(procInstId)) {
            throw new BusinessException("procInstId不能为空", ErrorCodeConstant.REQUEST_PARAMETER_ERROR);
        }
        CommonLoggerUtil.addProcess(logger, "PermissionBpmsCallback", "Begin", objectKey, procInstId);
        Result result = permissionApplyService.permissionBpmsCallBack(objectKey,permissionRole, procInstId);

        return result;
    }

}
