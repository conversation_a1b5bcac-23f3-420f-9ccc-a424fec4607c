package com.taobao.wireless.orange.console.manager.impl;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.taobao.mappcenter.client.Result;
import com.taobao.mappcenter.client.constant.AppOsTypeEnum;
import com.taobao.mappcenter.client.constant.PhoneTypeEnum;
import com.taobao.mappcenter.client.constant.ProductNameEnum;
import com.taobao.mappcenter.client.request.client.MappClientInfo;
import com.taobao.mappcenter.client.request.query.PageQuery;
import com.taobao.mappcenter.client.service.console.MappInfoReadConsoleService;
import com.taobao.mappcenter.client.view.MappAppBaseView;
import com.taobao.mappcenter.client.view.MappPageResultView;
import com.taobao.wireless.orange.console.manager.AppManager;
import com.taobao.wireless.orange.console.manager.util.LangUtil;
import com.taobao.wireless.orange.console.manager.util.logger.CommonLoggerUtil;
import com.taobao.wireless.orange.core.common.Constant;
import com.taobao.wireless.orange.core.service.model.AppBO;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class AppManagerImpl implements AppManager {


    @Autowired
    private MappInfoReadConsoleService mappInfoReadConsoleService;

    private final static String APP_NAME = "orange-console";
    private static final MappClientInfo MAPP_CLIENT_INFO = new MappClientInfo();

    static {
        MAPP_CLIENT_INFO.setServerName(APP_NAME);
    }

    public final static String PRODUCT_IDENTIFIER = "productIdentifier";
    public final static String MTL_ID = "mtlId";


    @Override
    public AppBO selectAppByAppKey(String appKey) {
        if (StringUtils.isBlank(appKey)) {
            throw new IllegalArgumentException("appKey is null");
        }
        try {
            Result<MappAppBaseView> result;
            result = mappInfoReadConsoleService.getBaseAppInfoByAppKey(appKey, MAPP_CLIENT_INFO);
            if (!result.isSuccessful() || null == result.getData()) {
                CommonLoggerUtil.addException("AppManagerImpl", "selectAppByAppKey", "getBaseAppInfoByAppKey", "appKey=" + appKey + ", message=" + result.getMessage() + ",data=" + result.getData() != null, null);
                return null;
            }
            MappAppBaseView mappAppBaseView = result.getData();
            Result<Map<String, String>> extAttrResult = mappInfoReadConsoleService.getExtAppInfoByAppId(mappAppBaseView.getAppId(), MAPP_CLIENT_INFO);
            if (!extAttrResult.isSuccessful()) {
                CommonLoggerUtil.addException("AppManagerImpl", "selectAppByAppKey", "getExtAppInfoByAppId", "appKey=" + appKey + ",message=" + result.getMessage(), null);
                return null;
            }
            //获取应用标识
            String productIdentifier = null;
            String mtlId = null;
            if (LangUtil.isNotEmpty(extAttrResult.getData())) {
                productIdentifier = extAttrResult.getData().get(PRODUCT_IDENTIFIER);
                mtlId = extAttrResult.getData().get(MTL_ID);
            }
            return convert2AppBO(mappAppBaseView, productIdentifier, mtlId);
        } catch (Throwable t) {
            CommonLoggerUtil.addException("AppManagerImpl", "selectAppByAppKey", "getExtAppInfoByAppId", "appKey=" + appKey, t);

        }
        return null;
    }

    @Override
    public List<AppBO> selectAppList() {
        PageQuery pageQuery = new PageQuery();
        pageQuery.setCurrentPageNo(1);
        pageQuery.setPageRecordNo(1500);
        Result<MappPageResultView<MappAppBaseView>> result = null;
        List<AppBO> appBOs = new ArrayList<>(512);
        try {
            result = mappInfoReadConsoleService.getBaseAppInfoListByProductName(ProductNameEnum.ORANGE.name(), pageQuery, MAPP_CLIENT_INFO);
            if (!result.isSuccessful() || null == result.getData() || LangUtil.isEmpty(result.getData().getRetList())) {
                CommonLoggerUtil.addException("AppManagerImpl", "selectAppList", "getBaseAppInfoListByProductName", result.getCode() + ", message=" + result.getMessage() + ",data=" + result.getData() != null, null);
                return appBOs;
            }
            List<MappAppBaseView> mappApps = result.getData().getRetList();

            for (MappAppBaseView mappAppBaseView : mappApps) {
                appBOs.add(convert2AppBO(mappAppBaseView, null, null));
            }
        } catch (Throwable t) {
            CommonLoggerUtil.addException("AppManagerImpl", "selectAppList", "getBaseAppInfoListByProductName", null, t);

        }
        return appBOs;
    }

    @Override
    public Map<String/*appKey*/, AppBO> getAppMap() {
        List<AppBO> apps = selectAppList();
        Map<String, AppBO> appMap = Maps.newHashMap();
        for (AppBO app : apps) {
            appMap.put(app.getAppKey(), app);
        }
        return appMap;
    }

    @Override
    public boolean isAppExistAndOpenOrange(String appKey) {
        Preconditions.checkArgument(StringUtils.isNotBlank(appKey), "appKey参数为空");
        Map<String/*appKey*/, AppBO> appMap = getAppMap();
        return appMap != null && appMap.containsKey(appKey);
    }

    /**
     * 转换成AppBO对象
     *
     * @param mappAppBaseView
     * @return
     */
    private static final AppBO convert2AppBO(final MappAppBaseView mappAppBaseView, String productIdentifier, String mtlId) {
        AppOsTypeEnum osTypeEnum = AppOsTypeEnum.getOSTypeByCode(mappAppBaseView.getOsType());
        String appOsName = (osTypeEnum != null ? osTypeEnum : AppOsTypeEnum.UNKOWN).name().toLowerCase();
        AppBO appBO = new AppBO();
        appBO.setAppId(mappAppBaseView.getAppId());
        appBO.setAppDetail(mappAppBaseView.getMemo());
        appBO.setAppKey(mappAppBaseView.getCurrentAppKey());
        appBO.setAppName(mappAppBaseView.getAppName() + "-" + appOsName);
        appBO.setOsType(mappAppBaseView.getOsType());
        appBO.setAppPackageName(mappAppBaseView.getPackageName());
        appBO.setAppMark(productIdentifier);
        appBO.setMotuAppId(toMotuAppId(mappAppBaseView));
        appBO.setMtlId(mtlId);
        appBO.setBasicAppKey(mappAppBaseView.getBasicAppKey());
        appBO.setGmtCreate(mappAppBaseView.getGmtCreate());
        appBO.setIsAvailable(mappAppBaseView.getStatus() == 0 ? Constant.Y : Constant.N);
        return appBO;
    }

    protected static String toMotuAppId(MappAppBaseView mappAppBaseView) {
        StringBuilder sb = new StringBuilder();
        if (mappAppBaseView.getCurrentAppKey().equalsIgnoreCase("23552352")) {
            return "23552352@ipad";
        }
        if (!StringUtils.isEmpty(mappAppBaseView.getBasicAppKey())) {
            sb.append(mappAppBaseView.getBasicAppKey());

        } else {
            sb.append(mappAppBaseView.getCurrentAppKey());
        }
        final AppOsTypeEnum osType = AppOsTypeEnum.getOSTypeByCode(mappAppBaseView.getOsType());
        if (osType == null) {
            sb.append("@XXX");
            return sb.toString();
        }
        sb.append("@");
        switch (osType) {
            case ANDROID:
                if (mappAppBaseView.getPhoneType() == PhoneTypeEnum.PAD.getCode()) {
                    sb.append("apad");
                } else {
                    sb.append("android");
                }
                break;
            case IOS:
                if (mappAppBaseView.getPhoneType() == PhoneTypeEnum.PAD.getCode()) {
                    sb.append("ipad");
                } else {
                    sb.append("iphoneos");
                }
                break;
            case WINDOWS_PC:
                sb.append("windows");
                break;
            case HARMONY:
                sb.append("harmony");
                break;
            default:
                sb.append(osType.getName().toLowerCase());
                break;
        }
        return sb.toString();
    }
}
