<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
	   http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd"
       default-autowire="byName">

    <context:component-scan base-package="com.taobao.mappcenter"></context:component-scan>

    <bean id="mappCenterSdkConfig" class="com.taobao.mappcenter.sdk.config.MappCenterSdkConfig">
    </bean>

    <!--请求端信息,合一机房必须配置-->
    <bean id="mappClientConfig" class="com.taobao.mappcenter.config.MappClientConfig">
        <property name="clusterName" value="${mappcenter.service.consumer.cluster}"/><!--集群信息, ALI或者HEYI, 合一机房必须配-->
        <property name="serverName"  value="orange"/><!--aone应用名，可不配,直接读取系统应用名-->
    </bean>


    <bean id="appSecretService" class="com.taobao.wireless.orange.core.service.impl.AppSecretServiceImpl">
    </bean>


</beans>