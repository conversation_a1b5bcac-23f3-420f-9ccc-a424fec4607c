<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE beans PUBLIC "-//SPRING//DTD BEAN//EN" "http://www.springframework.org/dtd/spring-beans.dtd">
<beans>

    <bean id="dataSource" class="com.taobao.tddl.group.jdbc.TGroupDataSource" init-method="init">
        <property name="appName" value="${orange.db.appName}"/>
        <property name="dbGroupKey" value="${orange.db.dbGroupKey}"/>
        <property name="unitName" value="${orange.db.unitName}"/>
    </bean>


    <bean id="sqlMapClient" class="org.springframework.orm.ibatis.SqlMapClientFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="configLocation" value="classpath:orange-core/dao/sqlmap/sqlmap-config.xml"/>
    </bean>

    <bean id="baseDAO" abstract="true">
        <property name="sqlMapClient" ref="sqlMapClient"/>
    </bean>


    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <bean id="transactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
        <property name="transactionManager" ref="transactionManager"/>
        <!-- <property name="isolationLevelName" value="ISOLATION_DEFAULT"/> -->
        <!-- <property name="propagationBehaviorName" value="PROPAGATION_REQUIRED"/> -->
    </bean>

</beans>