<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="orange_namespace_gray_record">
  <resultMap class="com.taobao.wireless.orange.core.dao.model.NamespaceGrayRecordDO" id="BaseResultMap">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="namespace_id" jdbcType="VARCHAR" property="namespaceId" />
    <result column="app_key" jdbcType="VARCHAR" property="appKey" />
    <result column="namespace_name" jdbcType="VARCHAR" property="namespaceName" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="change_version" jdbcType="VARCHAR" property="changeVersion" />
    <result column="gray_ratio" jdbcType="INTEGER" property="grayRatio" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
  </resultMap>
  <resultMap class="com.taobao.wireless.orange.core.dao.model.NamespaceGrayRecordDO" extends="orange_namespace_gray_record.BaseResultMap" id="ResultMapWithBLOBs">
    <result column="gray_metas" jdbcType="LONGVARCHAR" property="grayMetas" />
  </resultMap>
  <sql id="condition">
    <dynamic prepend="where">
      <isNotNull prepend="AND" property="appKey">
        app_key = #appKey:VARCHAR#
      </isNotNull>
      <isNotNull prepend="AND" property="namespaceName">
        namespace_name = #namespaceName:VARCHAR#
      </isNotNull>
      <isNotNull prepend="AND" property="namespaceId">
        namespace_id = #namespaceId:VARCHAR#
      </isNotNull>
      <isNotNull prepend="AND" property="version">
        version = #version:VARCHAR#
      </isNotNull>
      <isNotNull prepend="AND" property="changeVersion">
        change_version = #changeVersion:VARCHAR#
      </isNotNull>
      <isNotNull prepend="AND" property="status">
        status = #status:VARCHAR#
      </isNotNull>
    </dynamic>
  </sql>
  <sql id="Base_Column_List">
    id, namespace_id, app_key, namespace_name, version, change_version, gray_ratio, status, gmt_create, gmt_modified
  </sql>
  <sql id="Blob_Column_List">
    gray_metas
  </sql>
  <select id="selectByPrimaryKey" parameterClass="com.taobao.wireless.orange.core.dao.model.NamespaceGrayRecordDO" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from orange_namespace_gray_record
    where id = #id:BIGINT#
  </select>
  <delete id="deleteByPrimaryKey" parameterClass="com.taobao.wireless.orange.core.dao.model.NamespaceGrayRecordDO">
    delete from orange_namespace_gray_record
    where id = #id:BIGINT#
  </delete>
  <insert id="insert" parameterClass="com.taobao.wireless.orange.core.dao.model.NamespaceGrayRecordDO">
    insert into orange_namespace_gray_record (namespace_id, app_key, namespace_name, version,
    change_version, gray_ratio, gray_metas, status, gmt_create, gmt_modified)
    values (#namespaceId:VARCHAR#, #appKey:VARCHAR#, #namespaceName:VARCHAR#, #version:VARCHAR#,
    #changeVersion:VARCHAR#, #grayRatio:INTEGER#, #grayMetas:LONGVARCHAR#, #status:VARCHAR#, now(), now())
    <selectKey keyProperty="id" resultClass="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
  </insert>
  <insert id="insertSelective" parameterClass="com.taobao.wireless.orange.core.dao.model.NamespaceGrayRecordDO">
    insert into orange_namespace_gray_record
    <dynamic prepend="(">
      gmt_create,
      gmt_modified
      <isNotNull prepend="," property="namespaceId">
        namespace_id
      </isNotNull>
      <isNotNull prepend="," property="appKey">
        app_key
      </isNotNull>
      <isNotNull prepend="," property="namespaceName">
        namespace_name
      </isNotNull>
      <isNotNull prepend="," property="version">
        version
      </isNotNull>
      <isNotNull prepend="," property="changeVersion">
        change_version
      </isNotNull>
      <isNotNull prepend="," property="grayRatio">
        gray_ratio
      </isNotNull>
      <isNotNull prepend="," property="grayMetas">
        gray_metas
      </isNotNull>
      <isNotNull prepend="," property="status">
        status
      </isNotNull>
      )
    </dynamic>
    values
    <dynamic prepend="(">
      now(),
      now()
      <isNotNull prepend="," property="namespaceId">
        #namespaceId:VARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="appKey">
        #appKey:VARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="namespaceName">
        #namespaceName:VARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="version">
        #version:VARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="changeVersion">
        #changeVersion:VARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="grayRatio">
        #grayRatio:INTEGER#
      </isNotNull>
      <isNotNull prepend="," property="grayMetas">
        #grayMetas:LONGVARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="status">
        #status:VARCHAR#
      </isNotNull>
      )
    </dynamic>
    <selectKey keyProperty="id" resultClass="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterClass="com.taobao.wireless.orange.core.dao.model.NamespaceGrayRecordDO">
    update orange_namespace_gray_record
    <dynamic prepend="set">
      gmt_modified = now()
      <isNotNull prepend="," property="namespaceId">
        namespace_id = #namespaceId:VARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="appKey">
        app_key = #appKey:VARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="namespaceName">
        namespace_name = #namespaceName:VARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="version">
        version = #version:VARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="changeVersion">
        change_version = #changeVersion:VARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="grayRatio">
        gray_ratio = #grayRatio:INTEGER#
      </isNotNull>
      <isNotNull prepend="," property="grayMetas">
        gray_metas = #grayMetas:LONGVARCHAR#
      </isNotNull>
      <isNotNull prepend="," property="status">
        status = #status:VARCHAR#
      </isNotNull>
    </dynamic>
    where id = #id:BIGINT#
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterClass="com.taobao.wireless.orange.core.dao.model.NamespaceGrayRecordDO">
    update orange_namespace_gray_record
    set namespace_id = #namespaceId:VARCHAR#,
      app_key = #appKey:VARCHAR#,
      namespace_name = #namespaceName:VARCHAR#,
      version = #version:VARCHAR#,
      change_version = #changeVersion:VARCHAR#,
      gray_ratio = #grayRatio:INTEGER#,
      gmt_modified = now(),
      gray_metas = #grayMetas:LONGVARCHAR#,
      status = #status:VARCHAR#
    where id = #id:BIGINT#
  </update>
  <update id="updateByPrimaryKey" parameterClass="com.taobao.wireless.orange.core.dao.model.NamespaceGrayRecordDO">
    update orange_namespace_gray_record
    set namespace_id = #namespaceId:VARCHAR#,
      app_key = #appKey:VARCHAR#,
      namespace_name = #namespaceName:VARCHAR#,
      version = #version:VARCHAR#,
      change_version = #changeVersion:VARCHAR#,
      gray_ratio = #grayRatio:INTEGER#,
      status = #status:VARCHAR#,
      gmt_modified = now()
    where id = #id:BIGINT#
  </update>
  <update id="updateStatusByNamespaceChange" parameterClass="java.util.Map">
    update orange_namespace_gray_record
    set status = #status:VARCHAR#
    where namespace_id = #namespaceId:VARCHAR# and change_version = #changeVersion:VARCHAR#
  </update>
  <select id="select" parameterClass="java.util.Map" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from orange_namespace_gray_record
    <include refid="condition" />
    <include refid="global.order_by" />
    <include refid="global.limit" />
  </select>
</sqlMap>
