<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="namespaceVersion">

  <typeAlias alias="tableNamespaceVersionDO" type="com.taobao.wireless.orange.core.dao.model.NamespaceVersionDO" />

  <resultMap id="tableResult" class="tableNamespaceVersionDO" extends="global.abstractBaseResult">
    <result property="namespaceId" column="namespace_id" javaType="string" jdbcType="VARCHAR" />
    <result property="name" column="name" javaType="string" jdbcType="VARCHAR" />
    <result property="type" column="type" javaType="int" jdbcType="TINYINT" />
    <result property="loadLevel" column="load_level" javaType="int" jdbcType="TINYINT" />
    <result property="version" column="version" javaType="string" jdbcType="VARCHAR" />
    <result property="resourceId" column="resource_id" javaType="string" jdbcType="VARCHAR" />
    <result property="md5" column="md5" javaType="string" jdbcType="VARCHAR" />
    <result property="contentMd5" column="content_md5" javaType="string" jdbcType="VARCHAR" />
    <result property="appKey" column="app_key" javaType="string" jdbcType="VARCHAR" />
    <result property="appVersion" column="app_version" javaType="string" jdbcType="VARCHAR" />
    <result property="status" column="status" javaType="int" jdbcType="TINYINT" />
    <result property="source" column="source" javaType="int" jdbcType="TINYINT" />
    <result property="creator" column="creator" javaType="string" jdbcType="VARCHAR" />
    <result property="reviewer" column="reviewer" javaType="string" jdbcType="VARCHAR" />
    <result property="sourceData" column="source_data" javaType="string" jdbcType="VARCHAR" />
    <result property="previousResourceId" column="previous_resource_id" javaType="string" jdbcType="VARCHAR" />
    <result property="gmtPublish" column="gmt_publish" javaType="date" jdbcType="DATETIME"/>
    <result property="deleter" column="deleter" javaType="string" jdbcType="VARCHAR"/>
    <result property="strategy" column="strategy" javaType="string" jdbcType="VARCHAR"/>
    <result property="overwriteStrategyVersions" column="overwrite_strategy_versions" javaType="string" jdbcType="VARCHAR"/>
    <result property="versions" column="versions" javaType="string" jdbcType="VARCHAR"/>
    <result property="offlines" column="offlines" javaType="string" jdbcType="VARCHAR"/>
    <result property="isEmergent" column="is_emergent" javaType="string" jdbcType="VARCHAR"/>
    <result property="grayVersions" column="gray_versions" javaType="string" jdbcType="VARCHAR"/>
    <result property="tigaMetadata" column="tiga_metadata" javaType="string" jdbcType="JSON"/>

  </resultMap>

  <resultMap id="namespaceInfo" class="com.taobao.wireless.orange.core.dao.model.NamespaceInfoDO">
    <result property="appKey" column="app_key" javaType="string" jdbcType="VARCHAR"/>
    <result property="appVersion" column="app_version" javaType="string" jdbcType="VARCHAR"/>
    <result property="name" column="name" javaType="string" jdbcType="VARCHAR"/>
    <result property="amt" column="amt" javaType="int" jdbcType="INTEGER"/>
  </resultMap>

  <!-- 通用模板sql -->
  <insert id="insert" parameterClass="java.util.Map">
    <![CDATA[
      insert into
        orange_namespace_version
      (
        gmt_create, gmt_modified, is_available,


        namespace_id,
        name,
        type,
        load_level,
        version,
        resource_id,
        md5,
        content_md5,
        app_key,
        app_version,
        status,
        source,
        creator,
        reviewer,
        source_data,
        previous_resource_id,
        gmt_publish,
        deleter,
        strategy,
        overwrite_strategy_versions,
        versions,
        offlines,
        is_emergent,
        gray_versions,
        tiga_metadata


      )
      values (
        now(), now(), #is_available#,


        #namespace_id#,
        #name#,
        #type#,
        #load_level#,
        #version#,
        #resource_id#,
        #md5#,
        #content_md5#,
        #app_key#,
        #app_version#,
        #status#,
        #source#,
        #creator#,
        #reviewer#,
        #source_data#,
        #previous_resource_id#,
        now(),
        #deleter#,
        #strategy#,
        #overwrite_strategy_versions#,
        #versions#,
        #offlines#,
        #is_emergent#,
        #gray_versions#,
        #tiga_metadata#
      )
      ]]>
  </insert>

  <sql id="condition">

    <dynamic prepend="where">

      <isNotEmpty property="is_available" prepend="and">
        <![CDATA[
        is_available = #is_available#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="gmt_publish_s" prepend="and">
        <![CDATA[
        gmt_publish >= #gmt_publish_s#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="gmt_publish_e" prepend="and">
        <![CDATA[
        gmt_publish <= #gmt_publish_e#
        ]]>
      </isNotEmpty>


      <isNotEmpty property="gmt_create_s" prepend="and">
        <![CDATA[
        gmt_create >= #gmt_create_s#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="gmt_create_e" prepend="and">
        <![CDATA[
        gmt_create <= #gmt_create_e#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="gmt_modified_s" prepend="and">
        <![CDATA[
        gmt_modified >= #gmt_modified_s#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="gmt_modified_e" prepend="and">
        <![CDATA[
        gmt_modified <= #gmt_modified_e#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="namespace_id" prepend="and">
        <![CDATA[
        namespace_id = #namespace_id#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="name" prepend="and">
        <![CDATA[
        name = #name#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="type" prepend="and">
        <![CDATA[
        type = #type#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="load_level" prepend="and">
        <![CDATA[
        load_level = #load_level#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="version" prepend="and">
        <![CDATA[
        version = #version#
        ]]>
      </isNotEmpty>
      <isNotNull property="version_list" prepend="and">
          version in
          <iterate property="version_list" open="(" close=")" conjunction=",">
              #version_list[]#
          </iterate>
      </isNotNull>
      <isNotNull property="status_list" prepend="and">
          status in
          <iterate property="status_list" open="(" close=")" conjunction=",">
              #status_list[]#
          </iterate>
      </isNotNull>
      <isNotEmpty property="resource_id" prepend="and">
        <![CDATA[
        resource_id = #resource_id#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="md5" prepend="and">
        <![CDATA[
        md5 = #md5#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="content_md5" prepend="and">
        <![CDATA[
        content_md5 = #content_md5#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="app_key" prepend="and">
        <![CDATA[
        app_key = #app_key#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="app_version" prepend="and">
        <![CDATA[
        app_version = #app_version#
        ]]>
      </isNotEmpty>


      <isNotEmpty property="status" prepend="and">
        <![CDATA[
        status = #status#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="source" prepend="and">
        <![CDATA[
        source = #source#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="ignore_source" prepend="and">
        <![CDATA[
        source != #ignore_source#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="creator" prepend="and">
        <![CDATA[
        creator = #creator#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="reviewer" prepend="and">
        <![CDATA[
        reviewer = #reviewer#
        ]]>
      </isNotEmpty>


      <isNotEmpty property="source_data" prepend="and">
        <![CDATA[
        source_data = #source_data#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="previous_resource_id" prepend="and">
        <![CDATA[
        previous_resource_id = #previous_resource_id#
        ]]>
      </isNotEmpty>
      <isEqual property="withStrategy" compareValue="true" prepend="and">
         <![CDATA[
        (strategy is not null AND strategy<>'')
        ]]>
      </isEqual>
      <isEqual property="withStrategy" compareValue="false" prepend="and">
          <![CDATA[
        (strategy is null or strategy='')
        ]]>
      </isEqual>
      <isNotEmpty property="greater_than_version" prepend="and">
        <![CDATA[
        (version > #greater_than_version#
        ]]>
      </isNotEmpty>
    </dynamic>

  </sql>


  <select id="select" parameterClass="java.util.Map" resultMap="tableResult">
    <![CDATA[
      select * from orange_namespace_version
    ]]>
    <include refid="condition" />
    <include refid="global.order_by" />
    <include refid="global.limit" />
  </select>

  <select id="selectForPagination" parameterClass="java.util.Map" resultMap="tableResult">
    <![CDATA[
      select * from orange_namespace_version
    ]]>
    <include refid="condition" />
    <include refid="global.order_by" />
    <include refid="global.limit_one_two" />
  </select>

  <select id="selectCount" parameterClass="java.util.Map"  resultClass="java.lang.Integer">
    <![CDATA[
      select count(id) from orange_namespace_version
    ]]>
    <include refid="condition" />
  </select>


  <!-- 通用模板sql -->

  <select id="selectList" parameterClass="java.util.Map" resultMap="tableResult">
    <![CDATA[
      select * from orange_namespace_version
    ]]>
    <include refid="condition" />
    <include refid="global.order_by" />
  </select>

  <select id="selectByNamespaceId" parameterClass="java.util.Map" resultMap="tableResult">
    <![CDATA[
      select * from orange_namespace_version where namespace_id = #namespace_id#
    ]]>
  </select>

  <select id="selectByResourceId" parameterClass="java.util.Map" resultMap="tableResult">
    <![CDATA[
      select * from orange_namespace_version where resource_id = #resource_id#
    ]]>
  </select>

  <select id="selectByNamespaceIdAndVersion" parameterClass="java.util.Map" resultMap="tableResult">
    <![CDATA[
      select * from orange_namespace_version where namespace_id = #namespace_id#  and  version = #version#
    ]]>
  </select>

  <select id="selectByNamespaceAndAppKeyAndAppVersionAndAvailableWithStrategy" parameterClass="java.util.Map" resultMap="tableResult">
    <![CDATA[
      select * from orange_namespace_version where namespace_id = #namespace_id#
      and is_available = 'y'
      and app_key = #app_key#
      and app_version = #app_version#
      and namespace_id = #namespace_id#
      and strategy is not null and strategy<>''
      order by version DESC
    ]]>
  </select>

  <select id="selectByNamespaceAndAppKeyAndAppVersionAndAvailableWithoutStrategy" parameterClass="java.util.Map" resultMap="tableResult">
    <![CDATA[
      select * from orange_namespace_version where namespace_id = #namespace_id#
      and is_available = 'y'
      and app_key = #app_key#
      and app_version = #app_version#
      and namespace_id = #namespace_id#
      and (strategy is null or strategy='')
      order by version DESC
    ]]>
  </select>

  <select id="selectListWithWaitPublish" parameterClass="java.util.Map" resultMap="tableResult">
    <![CDATA[
      select * from orange_namespace_version where is_available = 'n'
      and namespace_id = #namespace_id#
      and status != 255
      and status != 250
      and status != 200
    ]]>
    <dynamic>

      <isNotEmpty property="app_version" prepend="and">
        <![CDATA[
        app_version = #app_version#
        ]]>
      </isNotEmpty>

    </dynamic>
  </select>

  <select id="selectSpecificNamespaces" parameterClass="java.util.Map" resultMap="namespaceInfo">
    <![CDATA[
       SELECT app_key,app_version,name, COUNT(1) as amt from orange_namespace_version
       where status != 255 and is_available = 'n'
    ]]>
    <dynamic>
      <isNotEmpty property="gmt_create_s" prepend="and">
        <![CDATA[
        gmt_create >= #gmt_create_s#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="gmt_create_e" prepend="and">
        <![CDATA[
        gmt_create <= #gmt_create_e#
        ]]>
      </isNotEmpty>
    </dynamic>
    <![CDATA[
        GROUP BY app_key,app_version,name
        HAVING amt > #retain#
        ORDER BY COUNT(1)  desc
         LIMIT #limit#
      ]]>
  </select>

  <select id="selectListForClear" parameterClass="java.util.Map" resultMap="tableResult">
    <![CDATA[
      select * from orange_namespace_version
      where status != 255 and is_available = 'n'
    ]]>
    <dynamic>
      <isNotEmpty property="app_key" prepend="and">
        <![CDATA[
        app_key = #app_key#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="app_version" prepend="and">
        <![CDATA[
        app_version = #app_version#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="name" prepend="and">
        <![CDATA[
        name = #name#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="gmt_create_s" prepend="and">
        <![CDATA[
        gmt_create >= #gmt_create_s#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="gmt_create_e" prepend="and">
        <![CDATA[
        gmt_create <= #gmt_create_e#
        ]]>
      </isNotEmpty>
    </dynamic>
    <include refid="global.order_by"/>
    <![CDATA[ LIMIT #limit#]]>

  </select>

  <update id="updateStatusByResourceIdList" parameterClass="java.util.Map">
    <isNotEmpty property="resource_id_list">
      <![CDATA[
      update orange_namespace_version set gmt_modified = now(),status = #status#,reviewer= 'system'
     ]]>
      where resource_id in
      <iterate property="resource_id_list" open="(" close=")" conjunction=",">
        #resource_id_list[]#
      </iterate>
      and
      status != #status#
    </isNotEmpty>
  </update>

  <update id="updateByNamespaceIdAndVersion" parameterClass="java.util.Map">
    <![CDATA[
      update orange_namespace_version set gmt_modified = now()
     ]]>
    <dynamic>

      <isNotEmpty property="is_available" prepend=",">
        <![CDATA[
        is_available = #is_available#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="load_level" prepend=",">
        <![CDATA[
        load_level = #load_level#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="status" prepend=",">
        <![CDATA[
        status = #status#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="md5" prepend=",">
        <![CDATA[
        md5 = #md5#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="content_md5" prepend=",">
        <![CDATA[
        content_md5 = #content_md5#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="reviewer" prepend=",">
        <![CDATA[
        reviewer = #reviewer#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="previous_resource_id" prepend=",">
        <![CDATA[
        previous_resource_id = #previous_resource_id#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="gmt_publish" prepend=",">
        <![CDATA[
        gmt_publish = now()
        ]]>
      </isNotEmpty>

      <isNotEmpty property="deleter" prepend=",">
        <![CDATA[
        deleter = #deleter#
        ]]>
      </isNotEmpty>
      <isNotEmpty property="is_emergent" prepend=",">
        <![CDATA[
        is_emergent = #is_emergent#
        ]]>
      </isNotEmpty>
      <isNotEmpty property="versions" prepend=",">
        <![CDATA[
        versions = #versions#
        ]]>
      </isNotEmpty>
      <isNotEmpty property="offlines" prepend=",">
        <![CDATA[
        offlines = #offlines#
        ]]>
      </isNotEmpty>

      <isNotEmpty property="tiga_metadata" prepend=",">
        <![CDATA[
        tiga_metadata = #tiga_metadata#
        ]]>
      </isNotEmpty>


    </dynamic>
    <![CDATA[
    where namespace_id = #namespace_id#  and  version = #version#
    ]]>
  </update>

  <update id="updateUnAvailableByAppKeyAndAppVersion" parameterClass="java.util.Map">

    <![CDATA[
      update orange_namespace_version set gmt_modified = now() , is_available ='n' , deleter = 'system'
      where is_available = 'y'
      and app_key = #app_key#
      and app_version = #app_version#
      and namespace_id = #namespace_id#
     ]]>


  </update>


  <update id="updateUnAvailableByAppKey" parameterClass="java.util.Map">
    <![CDATA[
      update orange_namespace_version set gmt_modified = now() , is_available ='n' , deleter = 'system'
      where is_available = 'y'
      and app_key = #app_key#
      and namespace_id = #namespace_id#
     ]]>
  </update>

  <update id="updateClosedByAppKey" parameterClass="java.util.Map">
    <![CDATA[
      update orange_namespace_version set gmt_modified = now() , is_available ='n' , deleter = 'system', status = 250
      where
      app_key = #app_key#
      and namespace_id = #namespace_id#
      and status not in (200,250,255)
     ]]>
  </update>



  <update id="updateUnAvailableById" parameterClass="java.util.Map">
    <![CDATA[
      update orange_namespace_version set gmt_modified = now() , is_available ='n' , deleter = 'system'
      where is_available = 'y'
      and id = #id#
     ]]>
  </update>

  <delete id="deleteByNamespaceId" parameterClass="java.util.Map">

    <![CDATA[
      delete  from orange_namespace_version where namespace_id = #namespace_id#
     ]]>

  </delete>

  <delete id="deleteByNamespaceIdAndAppVersion" parameterClass="java.util.Map">

    <![CDATA[
      delete  from orange_namespace_version where namespace_id = #namespace_id# and app_version = #app_version#
     ]]>

  </delete>

  <delete id="deleteByNamespaceIdList" parameterClass="java.util.Map">
    <isNotEmpty property="namespace_id_list">
      <![CDATA[
      delete from orange_namespace_version where namespace_id in
     ]]>
      <iterate property="namespace_id_list" conjunction="," open="(" close=")">
        #namespace_id_list[]#
      </iterate>
    </isNotEmpty>
  </delete>


  <update id="updateUnAvailableByVersions" parameterClass="java.util.Map">
    <![CDATA[
      update orange_namespace_version set gmt_modified = now() , is_available ='n' , deleter = 'system'
      where is_available = 'y'
      and namespace_id = #namespace_id#
      and version in

     ]]>
    <iterate property="version_list" open="(" close=")" conjunction=",">
      #version_list[]#
    </iterate>
  </update>

  <update id="updateAvailableByVersions" parameterClass="java.util.Map">
    <![CDATA[
      update orange_namespace_version set gmt_modified = now() , is_available ='y'
      where is_available = 'n'
      and namespace_id = #namespace_id#
      and version in

     ]]>
    <iterate property="version_list" open="(" close=")" conjunction=",">
      #version_list[]#
    </iterate>
  </update>

    <delete id="deleteByResourceIdList" parameterClass="java.util.Map">
        <isNotEmpty property="resource_id_list">
            <![CDATA[
      delete from orange_namespace_version where status=255 and is_available ='n' and resource_id in
      ]]>
            <iterate open="(" close=")" conjunction="," property="resource_id_list">
                #resource_id_list[]#
            </iterate>
        </isNotEmpty>
    </delete>

</sqlMap>
