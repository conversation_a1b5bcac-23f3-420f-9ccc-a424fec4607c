package com.taobao.wireless.orange.core.dao;

import com.taobao.wireless.orange.core.BaseDaoJunitManager;
import com.taobao.wireless.orange.core.dao.common.GmtTimeCondition;
import com.taobao.wireless.orange.core.dao.model.IndexDO;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class IndexDAOTest extends BaseDaoJunitManager {
    @Resource
    private IndexDAO indexDAO;

    @Test
    public void testSelectAppIndexVersion() {
        GmtTimeCondition gmtTimeCondition = new GmtTimeCondition();
        gmtTimeCondition.setGmtModifiedS("2011-01-01 00:00:00");
        gmtTimeCondition.setGmtModifiedE("2044-01-01 00:00:00");
        List<IndexDO> list = indexDAO.selectUnAvailableList4Clear(gmtTimeCondition, 2);
        Assert.assertEquals(2, list.size());
    }
}