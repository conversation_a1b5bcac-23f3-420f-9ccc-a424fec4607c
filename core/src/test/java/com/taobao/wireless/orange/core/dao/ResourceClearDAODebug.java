package com.taobao.wireless.orange.core.dao;

import com.taobao.wireless.orange.core.BaseDaoDebug;
import com.taobao.wireless.orange.core.dao.common.GmtTimeCondition;
import com.taobao.wireless.orange.core.dao.common.OrderBy;
import com.taobao.wireless.orange.core.dao.common.Pagination;
import com.taobao.wireless.orange.core.dao.model.NamespaceVersionDO;
import com.taobao.wireless.orange.core.type.Source;
import com.taobao.wireless.orange.core.type.Status;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class ResourceClearDAODebug extends BaseDaoDebug {

    @Autowired
    private NamespaceChangeDAO namespaceChangeDAO;

    @Autowired
    private VersionRecordDAO versionRecordDAO;
    @Autowired
    private NamespaceVersionDAO namespaceVersionDAO;

    @Test
    public void testVersionList() {
        boolean delete = true;
        NamespaceVersionDO query = new NamespaceVersionDO();
        query.setStatus(Status.CONTENT_NOT_EXIST.getCode());
        query.setSource(Source.ORANGE.getCode());
        GmtTimeCondition gmtTimeCondition = new GmtTimeCondition();
        gmtTimeCondition.setGmtCreateS("2020-03-01 00:00:00");
        gmtTimeCondition.setGmtCreateE("2020-05-01 00:00:00");
        List<NamespaceVersionDO> list = namespaceVersionDAO.selectForPagination(query, null, gmtTimeCondition, OrderBy.ORDER_BY_ID, new Pagination(1));
        if (CollectionUtils.isNotEmpty(list)) {
            NamespaceVersionDO ver = list.get(0);
            System.out.println(String.format(">>>namespaceId=%s,version=%s", ver.getNamespaceId(), ver.getVersion()));
            if (delete) {
                int cnt1 = namespaceChangeDAO.deleteByNamespaceVersion(ver.getNamespaceId(), ver.getVersion());
                int cnt2 = versionRecordDAO.deleteByNamespaceVersion(ver.getNamespaceId(), ver.getVersion());
                System.out.println(String.format(">>>>delete change %d,delete record %d", cnt1, cnt2));
            }
        }

    }

}

