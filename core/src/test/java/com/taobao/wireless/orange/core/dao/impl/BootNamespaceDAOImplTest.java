package com.taobao.wireless.orange.core.dao.impl;

import com.taobao.wireless.orange.core.BaseDaoJunitManager;
import com.taobao.wireless.orange.core.dao.BootNamespaceDAO;
import com.taobao.wireless.orange.core.dao.common.GmtTimeCondition;
import com.taobao.wireless.orange.core.dao.common.OrderBy;
import com.taobao.wireless.orange.core.dao.common.OtherCondition;
import com.taobao.wireless.orange.core.dao.common.Pagination;
import com.taobao.wireless.orange.core.dao.model.BootNamespaceDO;
import com.taobao.wireless.orange.core.dao.model.query.BootNamespaceQO;
import com.taobao.wireless.orange.core.service.model.BootNamespaceBO;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

public class BootNamespaceDAOImplTest extends BaseDaoJunitManager {

    @Autowired
    private BootNamespaceDAO bootNamespaceDAO;

    @Test
    public void testInsertSelective() {
        BootNamespaceDO bootNamespaceDO = BootNamespaceDO.builder()
                .namespaceName("mtop_ssr_adapter")
                .appKey("21646297")
                .expireTime(new Date()).status(1).modifier("149016")
                .creator("149016")
                .build();
        try {
            Long insert = this.bootNamespaceDAO.insert(bootNamespaceDO);
            BootNamespaceDO bootNamespaceDO1 = this.bootNamespaceDAO.selectByPrimaryKey(insert);
            Assert.assertEquals("mtop_ssr_adapter", bootNamespaceDO1.getNamespaceName());
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }

        BootNamespaceQO bootNamespaceQO = new BootNamespaceQO();
        bootNamespaceQO.setAppKey("21646297");
        bootNamespaceQO.setNamespaceName("mtop_ssr_adapter");
        bootNamespaceQO.setStatus(1);
        bootNamespaceQO.setAvailable(true);
        List<BootNamespaceDO> bootNamespaceDOS = this.bootNamespaceDAO.selectByParam(bootNamespaceQO);
        Assert.assertEquals(0, bootNamespaceDOS.size());
        bootNamespaceQO.setAvailable(false);
        bootNamespaceDOS = this.bootNamespaceDAO.selectByParam(bootNamespaceQO);
        Assert.assertEquals(1, bootNamespaceDOS.size());
    }

    @Test
    public void testSelectForPagination() {
        BootNamespaceDO bootNamespaceDO = new BootNamespaceBO();
        Pagination pagination = new Pagination(10);
        List<BootNamespaceDO> bootNamespaceDOS = this.bootNamespaceDAO.selectForPagination(bootNamespaceDO, new OtherCondition(), new GmtTimeCondition(), OrderBy.ORDER_BY_ID, pagination);
        Assert.assertTrue(pagination.getItemCount() > 0);
        Assert.assertTrue(bootNamespaceDOS.size() <= 10);
    }
}
