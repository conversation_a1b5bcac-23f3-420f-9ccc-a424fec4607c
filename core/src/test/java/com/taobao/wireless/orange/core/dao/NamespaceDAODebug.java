package com.taobao.wireless.orange.core.dao;

import com.taobao.wireless.orange.core.BaseDaoDebug;
import com.taobao.wireless.orange.core.dao.common.GmtTimeCondition;
import com.taobao.wireless.orange.core.dao.common.OrderBy;
import com.taobao.wireless.orange.core.dao.common.OtherCondition;
import com.taobao.wireless.orange.core.dao.common.Pagination;
import com.taobao.wireless.orange.core.dao.model.NamespaceDO;
import com.taobao.wireless.orange.core.service.model.NamespaceBO;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Random;

public class NamespaceDAODebug extends BaseDaoDebug {

    @Autowired
    private NamespaceDAO namespaceDAO;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Test
    public void selectByNamespaceIdForUpdate() {

        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                NamespaceDO namespaceDO = namespaceDAO.selectByNamespaceIdForUpdate("70c2fc5b5a5c438f86ebcea3d5e1d4a7");
                System.out.println(ToStringBuilder.reflectionToString(namespaceDO, ToStringStyle.MULTI_LINE_STYLE));
            }
        });
    }

    @Test
    public void selectByNamespaceIdForUpdate2() {

        NamespaceDO namespaceDO = namespaceDAO.selectByNamespaceIdForUpdate("70c2fc5b5a5c438f86ebcea3d5e1d4a7");
        System.out.println(ToStringBuilder.reflectionToString(namespaceDO, ToStringStyle.MULTI_LINE_STYLE));

    }

    @Test
    public void selectByNamespaceId() {
        NamespaceDO namespaceDO = namespaceDAO.selectByNamespaceId("70c2fc5b5a5c438f86ebcea3d5e1d4a7");
        System.out.println(ToStringBuilder.reflectionToString(namespaceDO, ToStringStyle.MULTI_LINE_STYLE));
    }


    @Test
    public void update() {
        String modules = Math.abs(new Random().nextLong()) + "";
        NamespaceBO obj = new NamespaceBO();
        obj.setDevelopers("");
        obj.setReviewers("");
        obj.setTesters("");
        obj.setOwners("莫扎,孤星,翁叔");
        obj.setModules(modules);
        namespaceDAO.updateByNamespaceId("718ceb14f6ae4fc9936077d92a4ec8d5", obj);

        NamespaceDO namespaceDO = namespaceDAO.selectByNamespaceId("718ceb14f6ae4fc9936077d92a4ec8d5");
        Assert.assertEquals(modules, namespaceDO.getModules());

        namespaceDO = namespaceDAO.selectByNamespaceIdForUpdate("718ceb14f6ae4fc9936077d92a4ec8d5");
        Assert.assertEquals(modules, namespaceDO.getModules());
    }

    @Test
    public void testQueryListPage() {
        NamespaceDO condition = new NamespaceBO();
        condition.setAppKeyOrGroup("4272");
        OtherCondition otherCondition = new OtherCondition();
        GmtTimeCondition gmtTimeCondition = new GmtTimeCondition();
        OrderBy orderBy = OrderBy.ORDER_BY_ID_DESC;
        Pagination pagination = new Pagination(5);
        List list = namespaceDAO.selectForPagination(condition, otherCondition, gmtTimeCondition, orderBy, pagination);
        System.out.println(ToStringBuilder.reflectionToString(list, ToStringStyle.MULTI_LINE_STYLE));
    }


    @Test
    public void selectAvailableListByUser() {

        List<NamespaceDO> list = namespaceDAO.selectAvailableListByUser("lanyin.smz|39753|兰茵");
        System.out.println(ToStringBuilder.reflectionToString(list, ToStringStyle.MULTI_LINE_STYLE));
    }
}
