package com.taobao.wireless.orange.core.dao;

import com.taobao.wireless.orange.core.BaseDaoDebug;
import com.taobao.wireless.orange.core.common.AppVersionUtils;
import com.taobao.wireless.orange.core.common.Constant;
import com.taobao.wireless.orange.core.dao.common.OrderBy;
import com.taobao.wireless.orange.core.dao.model.AppDO;
import com.taobao.wireless.orange.core.dao.model.AppVersionDO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.UUID;

public class AppVersionDAODebug extends BaseDaoDebug {

    @Autowired
    private AppVersionDAO appVersionDAO;


    @Test
    public void insert() {

        this.aa("5.1.5");
        this.aa("5.11");
        this.aa("5.2.3");
        this.aa("5.23");
        this.aa("9.999");
        this.aa("12.34.56.78");
    }

    private void aa(String appVersion) {
        AppVersionDO obj = new AppVersionDO();
        obj.setAppKey("99999999");
        obj.setAppVersion(appVersion);
        obj.setAppVersionValue(AppVersionUtils.appVersionToValue(appVersion));
        obj.setType("test");
        obj.setIsAvailable(Constant.Y);
        appVersionDAO.insert(obj);
    }


    @Test
    public void select() {
        int i = appVersionDAO.queryAppVersionCount("36677", AppVersionDO.TYPE_UNKNOWN);
        System.out.println(i);
    }

}
