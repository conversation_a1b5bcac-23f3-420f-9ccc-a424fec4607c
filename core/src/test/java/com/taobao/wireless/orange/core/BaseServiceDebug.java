package com.taobao.wireless.orange.core;

import com.taobao.hsf.standalone.HSFEasyStarter;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "classpath:applicationContext.xml",
        "classpath:env.properties.debug.xml" })
public abstract class BaseServiceDebug {

    public BaseServiceDebug() {
        System.out.println("启动hsf单元测试服务.....");


        HSFEasyStarter.start("", "");

        System.out.println("hsf启动完毕 .....");
    }

}