package com.taobao.wireless.orange.core.dao;

import com.taobao.wireless.orange.core.BaseDaoJunitManager;
import com.taobao.wireless.orange.core.common.Constant;
import com.taobao.wireless.orange.core.dao.common.OrderBy;
import com.taobao.wireless.orange.core.dao.model.VersionRecordDO;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class VersionRecordDAOTest extends BaseDaoJunitManager {
    @Resource
    private VersionRecordDAO versionRecordDAO;

    private final static String NAMESPACE_ID = "3c35aab07b8f44da8030cbe3c2d17edf";
    private final static String VERSION = "2120220711112004390";
    private final static String APP_KEY = TEST_APP_KEY;

    @Test
    public void testSelect() {
        VersionRecordDO query = new VersionRecordDO();
        query.setNamespaceId(NAMESPACE_ID);
        List<VersionRecordDO> list = versionRecordDAO.select(query, OrderBy.ORDER_BY_ID, 2);
        Assert.assertEquals(2, list.size());
    }


    @Test
    public void testInsert() {
        VersionRecordDO record = new VersionRecordDO();
        record.setNamespaceId(NAMESPACE_ID);
        record.setVersion(VERSION);
        record.setAppKey(APP_KEY);
        record.setCreator(TEST_EMP_ID);
        record.setType(2);
        record.setStatus(200);
        record.setIsAvailable(Constant.Y);
        record.setVer(2);
        record.setParams("{}");
        record.setResult("{}");
        record.setCheckResult("{}");
        Long recordId = versionRecordDAO.insert(record);
        Assert.assertTrue(recordId > 0);
        VersionRecordDO dbDO = versionRecordDAO.selectById(recordId);
        Assert.assertNotNull(dbDO);
    }
}
