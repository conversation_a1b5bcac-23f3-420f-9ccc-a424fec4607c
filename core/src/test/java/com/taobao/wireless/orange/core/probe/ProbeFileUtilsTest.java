package com.taobao.wireless.orange.core.probe;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.taobao.wireless.orange.core.service.model.ProbeBO;
import com.taobao.wireless.orange.core.service.model.ProbeMetaItem;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ProbeFileUtilsTest {


    @Test
    public void testVersion() {
        Assert.assertFalse(DiffProbeFileUtils.isVersionValidate("yyyyMMddHHmmss"));
        Assert.assertFalse(DiffProbeFileUtils.isVersionValidate("202005010022"));
        Assert.assertFalse(DiffProbeFileUtils.isVersionValidate("202005010022000"));
        Assert.assertTrue(DiffProbeFileUtils.isVersionValidate("20200501002200"));


    }

    @Test
    public void testDiffEncode() {
        String defaultCdnHost = "orange.waptest.taobao.com";
        String protocol = "https";
        String version = "20220424095300";
        boolean diffDegrade = false;
        List<ProbeBO> list = new ArrayList<>();
        ProbeBO probe1 = new ProbeBO();
        probe1.setAppKey("60044578");
        probe1.setMetaList(JSONObject.parseArray("[{\"baseVersion\":\"0\",\"md5\":\"94ab75194240b32bb2c84adc5a4e9bab\",\"resourceId\":\"im7ff92bbadc254b7381c6f3fb19abe5e1.json\"}]", ProbeMetaItem.class));
        probe1.setChangeVersion("3120220424095215621");
        probe1.setIndexVersion("1120220424095300237");

        ProbeBO probe2 = new ProbeBO();
        probe2.setAppKey("60039542");
        probe2.setMetaList(JSONObject.parseArray("[{\"baseVersion\":\"1120220420143758235\",\"md5\":\"280e845c2a640dd2bc1f484fc42eb88a\",\"resourceId\":\"im280218df48b54fa78441e92f6f307e6a-1.json\"},{\"baseVersion\":\"0\",\"md5\":\"75f80b10a28a0c27b6cc20abaed54dbf\",\"resourceId\":\"im280218df48b54fa78441e92f6f307e6a.json\"}]", ProbeMetaItem.class));
        probe2.setChangeVersion("3120220422180041408");
        probe2.setIndexVersion("1120220422180041736");
        list.add(probe1);
        list.add(probe2);
        Map<String, String> appHostMap = ImmutableMap.of("60039542", "1120220422180041736");
        String value1 = DiffProbeFileUtils.createProbeFileString(defaultCdnHost, protocol, version, diffDegrade, list, appHostMap);
        diffDegrade = true;
        String value2 = DiffProbeFileUtils.createProbeFileString(defaultCdnHost, protocol, version, diffDegrade, list, appHostMap);


        System.out.println(value1);
        System.out.println(value2);
        Assert.assertEquals("orange.waptest.taobao.com,https,20220424095300\n" +
                "60044578,1120220424095300237,,0:im7ff92bbadc254b7381c6f3fb19abe5e1.json:94ab75194240b32bb2c84adc5a4e9bab\n" +
                "60039542,1120220422180041736,1120220422180041736,1120220420143758235:im280218df48b54fa78441e92f6f307e6a-1.json:280e845c2a640dd2bc1f484fc42eb88a;0:im280218df48b54fa78441e92f6f307e6a.json:75f80b10a28a0c27b6cc20abaed54dbf\n", value1);

        Assert.assertEquals("orange.waptest.taobao.com,https,20220424095300\n" +
                "60044578,1120220424095300237,,0:im7ff92bbadc254b7381c6f3fb19abe5e1.json:94ab75194240b32bb2c84adc5a4e9bab\n" +
                "60039542,1120220422180041736,1120220422180041736,0:im280218df48b54fa78441e92f6f307e6a.json:75f80b10a28a0c27b6cc20abaed54dbf\n", value2);


    }


    @Test
    public void testNewDecode() {
        String value = "dorangesource-pre.alicdn.com,https,20200421190400\r\n" +
                "21646297,1120200421190400447,,1120200421184300928:im103ad27b740b483f96c33540e06b37dc-1.json:442df6133b34efa7536ddf2de61c6671;1120200421152500811:im103ad27b740b483f96c33540e06b37dc-2.json:60c97a6c97b891857e608e404398669c;0:im103ad27b740b483f96c33540e06b37dc.json:66f08ac729c388199c2dc1272e53c2f7\n" +
                "24716707,1120200421190400242,dorangesource.alicdn.com,0:imdda9018467754cefa30610f9184d0616.json:cfc5a65c4c52eaab05a9cfcee99f6718\r\n" +
                "\r\n" +
                "21622010,1120200421185500234,,0:im464f00fdaafe44b1a4229a4f1af3727f.json:88b8378873ad0aa8fcefb9584328a103\r\n\r\n" +
                "\n" +
                "\r\n";
        DiffProbeFile probeFile = DiffProbeFileUtils.decodeProbeFile(value);
        Assert.assertNotNull(probeFile);
        List<DiffProbe> list = probeFile.getList();
        Assert.assertTrue(list.size() == 3);
        for (DiffProbe each : list) {
            List<ProbeMetaItem> metaList = each.getMetaList();
            int length = metaList.size();
            Assert.assertTrue(length > 0);
            Assert.assertTrue(!StringUtils.contains(metaList.get(length - 1).getMd5(), "\r"));
            if (StringUtils.equalsIgnoreCase(each.getAppKey(), "24716707")) {
                Assert.assertEquals("dorangesource.alicdn.com", each.getCdn());
            } else {
                Assert.assertEquals("dorangesource-pre.alicdn.com", each.getCdn());
            }
        }
        System.out.println(JSONObject.toJSONString(probeFile));
    }

    @Test
    public void testOldDecode() {
        ProbeFile probeFile = ProbeFileUtils.decodeProbeFile("orange.waptest.taobao.com^https\r\n" +
                "036677^*^1120170604180729863^0^imd5ff2ae87f9e4ae3bfac40daeef10276.json^ff2273e247a9446e0cb87dd0b4b8f7d7\r\n" +
                "\r\n" +
                "036677^11.55.9.9^1120170604180729863^1220170322201050894^im6679571424d54a998bbb9a3c24864f79.json^17faad277e9456442f7a4e0c13e4f2ea\n" +
                "036677^9.999.9.9^1120170604180729863^1220170406172132711^im22889c6aaea54fa1be936e62e0e4be28.json^71a4df73514d164b1dffb42a7f9e2f53\r\n" +
                "\r\n");
        ;


        Assert.assertNotNull(probeFile);
        List<Probe> list = probeFile.getList();
        Assert.assertTrue(list.size() == 3);
        for (Probe each : list) {
            Assert.assertTrue(StringUtils.isNotBlank(each.getResourceId()));

            Assert.assertTrue(!StringUtils.contains(each.getMd5(), "\r"));


        }
        System.out.println(JSONObject.toJSONString(probeFile));

    }

}
