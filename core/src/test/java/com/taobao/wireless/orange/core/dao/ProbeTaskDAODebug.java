package com.taobao.wireless.orange.core.dao;

import java.util.UUID;

import com.taobao.wireless.orange.core.BaseDaoDebug;
import com.taobao.wireless.orange.core.common.Constant;
import com.taobao.wireless.orange.core.dao.model.ProbeTaskDO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ProbeTaskDAODebug extends BaseDaoDebug {

    @Autowired
    private ProbeTaskDAO probeTaskDAO;

    @Test
    public void insert() {
        for (int i = 0; i < 10; i++) {
            ProbeTaskDO obj = new ProbeTaskDO();
            obj.setIsAvailable(Constant.Y);
            obj.setAppKey("appKey");
            obj.setNamespace(UUID.randomUUID().toString());
            probeTaskDAO.insert(obj);
        }

    }



}
