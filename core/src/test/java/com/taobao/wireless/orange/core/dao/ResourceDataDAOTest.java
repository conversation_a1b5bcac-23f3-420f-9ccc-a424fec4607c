package com.taobao.wireless.orange.core.dao;

import com.taobao.wireless.orange.core.BaseDaoJunitManager;
import com.taobao.wireless.orange.core.common.Constant;
import com.taobao.wireless.orange.core.common.Utils;
import com.taobao.wireless.orange.core.dao.common.OrderBy;
import com.taobao.wireless.orange.core.dao.model.ResourceDataDO;
import com.taobao.wireless.orange.core.type.ResourceType;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

public class ResourceDataDAOTest extends BaseDaoJunitManager {
    @Resource
    private ResourceDataDAO resourceDataDAO;

    private final static String APP_KEY = TEST_APP_KEY;

    @Test
    public void testSelect() {
        ResourceDataDO query = new ResourceDataDO();
        query.setAppKey(APP_KEY);
        List<ResourceDataDO> list = resourceDataDAO.select(query, OrderBy.ORDER_BY_ID, 2);
        Assert.assertEquals(2, list.size());
        Assert.assertEquals(APP_KEY, list.get(0).getAppKey());
    }


    @Test
    public void testSelectByType() {
        ResourceDataDO query = new ResourceDataDO();
        query.setType(ResourceType.NAMESPACE_BETA.getCode());
        List<ResourceDataDO> list = resourceDataDAO.select(query, OrderBy.ORDER_BY_ID, 2);
        Assert.assertEquals(2, list.size());
        Assert.assertEquals(ResourceType.NAMESPACE_BETA.getCode(), (int) list.get(0).getType());
    }

    @Test
    public void testInsert() {
        String uuid = Utils.UUID();
        String resourceId = "test-" + uuid + ".json";
        String content = uuid;
        String md5 = uuid;
        ResourceDataDO resourceDO = new ResourceDataDO();
        resourceDO.setAppKey(APP_KEY);
        resourceDO.setType(ResourceType.NAMESPACE_BETA.getCode());
        resourceDO.setResourceId(resourceId);
        resourceDO.setIsAvailable(Constant.Y);
        resourceDO.setSrcContent(content);
        resourceDO.setData(content);
        resourceDO.setMd5(md5);

        resourceDataDAO.insert(resourceDO);
        ResourceDataDO dbDO = resourceDataDAO.selectByResourceId(resourceId);
        Assert.assertNotNull(dbDO);
        Assert.assertNotNull(dbDO.getAppKey());
        Assert.assertNotNull(dbDO.getType());

    }

    @Test
    public void testSelectByResourceIds() {
        List<ResourceDataDO> resourceDataDOS = resourceDataDAO.selectByResourceIds(Arrays.asList("ia6bf6017ccaf849379c0be6c986c47e03.index", "iadb17b98607f343eaa053ba72f67f8d31.index"));
        Assert.assertEquals(2, resourceDataDOS.size());
        Assert.assertEquals("ia6bf6017ccaf849379c0be6c986c47e03.index", resourceDataDOS.get(0).getResourceId());
        Assert.assertEquals("iadb17b98607f343eaa053ba72f67f8d31.index", resourceDataDOS.get(1).getResourceId());
    }
}
