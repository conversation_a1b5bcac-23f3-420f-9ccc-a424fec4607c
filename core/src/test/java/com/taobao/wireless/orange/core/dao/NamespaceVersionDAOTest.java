package com.taobao.wireless.orange.core.dao;

import com.taobao.wireless.orange.core.BaseDaoJunitManager;
import com.taobao.wireless.orange.core.common.Constant;
import com.taobao.wireless.orange.core.dao.common.GmtTimeCondition;
import com.taobao.wireless.orange.core.dao.common.OrderBy;
import com.taobao.wireless.orange.core.dao.model.NamespaceInfoDO;
import com.taobao.wireless.orange.core.dao.model.NamespaceVersionDO;
import com.taobao.wireless.orange.core.type.Status;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

public class NamespaceVersionDAOTest extends BaseDaoJunitManager {
    @Resource
    private NamespaceVersionDAO namespaceVersionDAO;

    @Test
    public void testSelectSpecificNamespaces() {
        GmtTimeCondition condition = new GmtTimeCondition();
        condition.setGmtCreateE("2021-09-11 11:34:00");
        int retain = 5;
        int limit = 2;
        List<NamespaceInfoDO> list = namespaceVersionDAO.selectSpecificNamespaces(condition, retain, limit);
        Assert.assertEquals(limit, list.size());
        for (NamespaceInfoDO infoDO : list) {
            Assert.assertTrue(infoDO.getAmt() > retain);
        }
    }

    @Test
    public void testSelectClearList() {
        GmtTimeCondition condition = new GmtTimeCondition();
        condition.setGmtCreateE("2022-09-01 11:34:00");
        NamespaceVersionDO query = new NamespaceVersionDO();
        query.setAppKey("60039542");
        query.setAppVersion("*");
        query.setName("orange_test_junit_preplan_single");
        int limit = 2;
        List<NamespaceVersionDO> list = namespaceVersionDAO.selectListForClear(query, condition, OrderBy.ORDER_BY_ID, limit);
        Assert.assertTrue(limit >= list.size());
        for (NamespaceVersionDO versionDO : list) {
            Assert.assertTrue(!versionDO.isValid());
        }
    }

    @Test
    public void testDeleteByResourceIdList() {
        NamespaceVersionDO query = new NamespaceVersionDO();
        query.setStatus(Status.CONTENT_NOT_EXIST.getCode());
        query.setIsAvailable(Constant.N);
        List<NamespaceVersionDO> list = namespaceVersionDAO.select(query, OrderBy.ORDER_BY_ID, 2);
        if (!CollectionUtils.isEmpty(list)) {
            Assert.assertTrue(list.size() <= 2);
            for (NamespaceVersionDO each : list) {
                Assert.assertEquals(Status.CONTENT_NOT_EXIST.getCode(), (int) each.getStatus());
                Assert.assertEquals(Constant.N, each.getIsAvailable());
            }
            List<String> resourceIdList = list.stream().map(item -> item.getResourceId()).collect(Collectors.toList());
            int cnt = namespaceVersionDAO.deleteByResourceIdList(resourceIdList);
            Assert.assertEquals(list.size(), cnt);
        }
    }

}
