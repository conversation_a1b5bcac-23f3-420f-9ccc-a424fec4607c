package com.taobao.wireless.orange.core.dao;

import com.taobao.wireless.orange.core.BaseDaoDebug;
import com.taobao.wireless.orange.core.dao.common.OrderBy;
import com.taobao.wireless.orange.core.dao.model.IndexDO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class IndexDAODebug extends BaseDaoDebug {

    @Autowired
    private IndexDAO indexDAO;

    @Test
    public void test() {
        IndexDO obj = new IndexDO();
        obj.setIndexId("0a8348fbc6144de2a37705de1363b236");
        List<IndexDO> select = indexDAO.select(obj, OrderBy.ORDER_BY_GC_DESC, 1);
        for (IndexDO indexDO : select) {
            System.out.println(indexDO);
        }
    }

}
