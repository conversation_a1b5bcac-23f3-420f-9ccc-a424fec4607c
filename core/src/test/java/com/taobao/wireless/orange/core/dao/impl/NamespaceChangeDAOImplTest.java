package com.taobao.wireless.orange.core.dao.impl;

import com.taobao.wireless.orange.core.BaseDaoJunitManager;
import com.taobao.wireless.orange.core.dao.NamespaceChangeDAO;
import com.taobao.wireless.orange.core.dao.common.OrderBy;
import com.taobao.wireless.orange.core.dao.model.NamespaceChangeDO;
import com.taobao.wireless.orange.core.dao.model.query.NamespaceChangeQO;
import com.taobao.wireless.orange.core.type.ChangeStatus;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

public class NamespaceChangeDAOImplTest extends BaseDaoJunitManager {

    @Autowired
    private NamespaceChangeDAO namespaceChangeDAO;

    @Test
    public void testSelectList() {
        NamespaceChangeQO namespaceChangeQO = new NamespaceChangeQO();
        namespaceChangeQO.setNamespaceId("e13233f25cd1404ba03bf478910487b5");
        namespaceChangeQO.setStatusList(Arrays.asList(ChangeStatus.NEW.getCode(), ChangeStatus.GRAY.getCode()));
        List<NamespaceChangeDO> namespaceChangeDOS = this.namespaceChangeDAO.selectList(namespaceChangeQO, OrderBy.ORDER_BY_ID);
        Assert.assertTrue(namespaceChangeDOS.size() > 0);
    }
}
