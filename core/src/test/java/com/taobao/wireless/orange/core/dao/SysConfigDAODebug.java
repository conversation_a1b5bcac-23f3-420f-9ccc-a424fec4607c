package com.taobao.wireless.orange.core.dao;

import com.taobao.wireless.orange.core.BaseDaoDebug;
import com.taobao.wireless.orange.core.dao.model.SysConfigDO;
import com.taobao.wireless.orange.core.dao.param.SysConfigParam;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class SysConfigDAODebug extends BaseDaoDebug {

    @Autowired
    private SysConfigDAO sysConfigDAO;


    @Test
    public void insert() {
        SysConfigDO sysConfigDO = new SysConfigDO();
        sysConfigDO.setCode("report");
        sysConfigDO.setSubCode("global");
        String appKey = "133434";
        sysConfigDO.setConfigKey(appKey);
        sysConfigDO.setConfigValue("{}");
        sysConfigDO.setIsAvailable("y");
        sysConfigDAO.insert(sysConfigDO);
    }


    @Test
    public void select() {
        SysConfigParam sysConfigParam = new SysConfigParam();
        SysConfigParam.Criteria criteria = sysConfigParam.createCriteria()
                .andCodeEqualTo("report")
                .andSubCodeEqualTo("global");
        List<SysConfigDO> sysConfigDOS = sysConfigDAO.selectByParamWithBLOBs(sysConfigParam);
        System.out.println(sysConfigDOS);
    }
}
