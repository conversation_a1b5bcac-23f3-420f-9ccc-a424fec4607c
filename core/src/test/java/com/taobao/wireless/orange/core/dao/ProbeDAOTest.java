package com.taobao.wireless.orange.core.dao;

import com.taobao.wireless.orange.core.BaseDaoJunitManager;
import com.taobao.wireless.orange.core.common.Constant;
import com.taobao.wireless.orange.core.dao.common.OrderBy;
import com.taobao.wireless.orange.core.dao.model.ProbeDO;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class ProbeDAOTest extends BaseDaoJunitManager {
    @Resource
    private ProbeDAO probeDAO;

    @Test
    public void testSelectAppIndexVersion() {

        List<String> indexVersionList = Arrays.asList("1120200228214900662", "1120200303204119155");
        List<ProbeDO> list = probeDAO.selectByIndexVersionList(indexVersionList);
        Assert.assertEquals(2, list.size());
    }


    @Test
    public void testDelete() {
        ProbeDO query = new ProbeDO();
        query.setAppKey("60039542");
        query.setIsAvailable(Constant.N);
        List<ProbeDO> list = probeDAO.select(query, OrderBy.ORDER_BY_ID, 2);
        Assert.assertEquals(2, list.size());
        list.stream().forEach(item -> {
            Assert.assertEquals(Constant.N, item.getIsAvailable());
        });

        List<String> indexVersionList = list.stream().map(item -> item.getIndexVersion()).collect(Collectors.toList());
        int cnt = probeDAO.deleteByIndexVersionList(indexVersionList);
        Assert.assertEquals(2, cnt);

    }
}
